<?php

namespace Database\Seeders;

use App\Models\SwService;
use Illuminate\Database\Seeder;

class SwServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'service_name' => '컴퓨터/프린터 등 IT 기기 수리 요청 게시판',
                'service_detail' => 'IT 기기 수리 요청을 관리하는 게시판 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '웹사이트 모음(schooLinker:학교용)',
                'service_detail' => '학교에서 자주 사용하는 웹사이트 모음 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '웹사이트 모음(ULinker:개인용)',
                'service_detail' => '개인이 자주 사용하는 웹사이트 모음 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '웹사이트 모음(Linkers:개인용)-무료',
                'service_detail' => '개인이 무료로 이용할 수 있는 웹사이트 모음 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '간편 전자 결재 서비스',
                'service_detail' => '간편하게 사용할 수 있는 전자 결재 시스템입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '컴퓨터 메모리 및 IP주소 확인 서비스',
                'service_detail' => '컴퓨터의 메모리 사용량과 IP 주소를 확인할 수 있는 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '원페이지 서비스',
                'service_detail' => '한 페이지에 모든 정보를 담아 제공하는 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '조퇴 확인 서비스 프린터 토너 요청 게시판',
                'service_detail' => '조퇴 확인 및 프린터 토너 요청을 관리하는 게시판 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '화장실 관리 요구서',
                'service_detail' => '화장실 관리 요청을 처리하는 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '건물 수리 요청 게시판',
                'service_detail' => '건물 수리 요청을 관리하는 게시판 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '과학실 준비물 요청 서비스',
                'service_detail' => '과학실 준비물 요청을 관리하는 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '컴퓨터 정보 수집 서비스',
                'service_detail' => '컴퓨터 사양 및 정보를 수집하는 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => 'IT 기기 대여 서비스',
                'service_detail' => 'IT 기기 대여를 관리하는 서비스입니다.',
                'service_state' => true,
            ],
            [
                'service_name' => '가정 통신문 채번 서비스',
                'service_detail' => '가정 통신문 번호를 관리하는 서비스입니다.',
                'service_state' => true,
            ],
        ];

        foreach ($services as $service) {
            SwService::create($service);
        }
    }
}