<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qna_answers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('qna_id')->constrained('qnas')->onDelete('cascade');
            $table->foreignId('parent_id')->nullable()->constrained('qna_answers')->onDelete('cascade');
            $table->foreignId('admin_id')->constrained('users');
            $table->string('title');
            $table->text('contents');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qna_answers');
    }
};