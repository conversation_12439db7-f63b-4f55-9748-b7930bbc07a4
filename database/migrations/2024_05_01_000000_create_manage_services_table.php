<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manage_services', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('school_admin_id');
            $table->unsignedBigInteger('school_code');
            $table->unsignedBigInteger('sw_service_id');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->foreign('school_admin_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('sw_service_id')->references('id')->on('sw_services')->onDelete('cascade');
            
            // 한 학교에서 각 서비스는 한 번만 활성화 가능
            $table->unique(['school_code', 'sw_service_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manage_services');
    }
};