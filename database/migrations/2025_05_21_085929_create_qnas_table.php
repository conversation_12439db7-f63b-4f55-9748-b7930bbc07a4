<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qnas', function (Blueprint $table) {
        $table->id();
        $table->integer('request_user_no');
        $table->string('question_title');
        $table->string('question_contents');
        $table->integer('question_state');
        $table->integer('answer_admin_no');
        $table->string('answer_title');
        $table->string('answer_contents');
        $table->string('note01');
        $table->string('note02');
        $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qnas');
    }
};
