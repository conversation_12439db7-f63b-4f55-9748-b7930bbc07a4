<div>
    <div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl p-4">
        <h2 class="text-xl font-bold">Q&A 관리</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 질문 목록 -->
            <div class="bg-white dark:bg-zinc-700 rounded-xl shadow p-4">
                <h3 class="text-lg font-semibold mb-4">질문 목록</h3>
                
                @if($questions && count($questions) > 0)
                    <ul class="divide-y divide-gray-200 dark:divide-gray-600">
                        @foreach($questions as $question)
                            <li class="py-3">
                                <button 
                                    wire:click="selectQuestion({{ $question->id }})"
                                    class="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-zinc-600 transition-colors
                                    {{ $selectedQuestion && $selectedQuestion->id == $question->id ? 'bg-blue-100 dark:bg-blue-900' : '' }}"
                                >
                                    <div class="font-medium">{{ $question->question_title }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ $question->question_contents }}</div>
                                    <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                        상태: {{ $question->question_state == 0 ? '답변 대기중' : '답변 완료' }}
                                    </div>
                                </button>
                            </li>
                        @endforeach
                    </ul>
                @else
                    <p class="text-gray-500 dark:text-gray-400">등록된 질문이 없습니다.</p>
                @endif
            </div>
            
            <!-- 선택된 질문 상세 및 답변 폼 -->
            <div class="bg-white dark:bg-zinc-700 rounded-xl shadow p-4 overflow-y-auto max-h-[80vh]">
                @if($selectedQuestion)
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-2">선택된 질문</h3>
                        <div class="bg-gray-50 dark:bg-zinc-800 p-4 rounded-md mb-4">
                            <h4 class="font-medium">{{ $selectedQuestion->question_title }}</h4>
                            <p class="mt-2 whitespace-pre-line">{{ $selectedQuestion->question_contents }}</p>
                        </div>
                        
                        <!-- 답변 목록 -->
                        @if($selectedQuestion->topLevelAnswers->count() > 0)
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-2">답변 목록</h3>
                                
                                @foreach($selectedQuestion->topLevelAnswers as $answer)
                                    <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-md mb-3">
                                        <div class="flex justify-between items-start">
                                            <h4 class="font-medium">{{ $answer->title }}</h4>
                                            <button 
                                                wire:click="replyToAnswer({{ $answer->id }})" 
                                                class="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                                            >
                                                답변하기
                                            </button>
                                        </div>
                                        <p class="mt-2 whitespace-pre-line">{{ $answer->contents }}</p>
                                        <div class="text-xs text-gray-500 mt-2">
                                            {{ $answer->created_at->format('Y-m-d H:i') }}
                                        </div>
                                        
                                        <!-- 답변에 대한 답변 (대댓글) -->
                                        @if($answer->replies->count() > 0)
                                            <div class="mt-3 pl-4 border-l-2 border-blue-200 dark:border-blue-800">
                                                @foreach($answer->replies as $reply)
                                                    <div class="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-md mb-2">
                                                        <div class="flex justify-between items-start">
                                                            <h5 class="font-medium text-sm">{{ $reply->title }}</h5>
                                                            <button 
                                                                wire:click="replyToAnswer({{ $reply->id }})" 
                                                                class="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                                                            >
                                                                답변하기
                                                            </button>
                                                        </div>
                                                        <p class="mt-1 text-sm whitespace-pre-line">{{ $reply->contents }}</p>
                                                        <div class="text-xs text-gray-500 mt-1">
                                                            {{ $reply->created_at->format('Y-m-d H:i') }}
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        
                        <!-- 답변 폼 -->
                        <form wire:submit.prevent="saveAnswer" class="mt-4">
                            <div class="mb-2">
                                @if($replyToAnswerId)
                                    <div class="bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-md mb-3 text-sm">
                                        답변에 대한 답변을 작성하고 있습니다.
                                        <button type="button" wire:click="resetForm" class="text-blue-600 dark:text-blue-400 hover:underline ml-2">
                                            취소
                                        </button>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="mb-4">
                                <label for="answer_title" class="block text-sm font-medium mb-2">답변 제목</label>
                                <input type="text" id="answer_title" wire:model="answer_title" 
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" required>
                                @error('answer_title') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                            </div>
                            
                            <div class="mb-4">
                                <label for="answer_contents" class="block text-sm font-medium mb-2">답변 내용</label>
                                <textarea id="answer_contents" wire:model="answer_contents" rows="6"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" required></textarea>
                                <!--@error('answer_contents') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror -->
                                
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                                    답변 등록하기
                                </button>
                            </div>
                        </form>
                    </div>
                @else
                    <div class="flex items-center justify-center h-full">
                        <p class="text-gray-500 dark:text-gray-400">질문을 선택하면 상세 내용이 표시됩니다.</p>
                    </div>
                @endif
            </div>
        </div>
        
        @if(session()->has('message'))
            <div class="mt-4 p-3 bg-green-100 text-green-700 rounded-md">
                {{ session('message') }}
            </div>
        @endif
    </div>
</div>
