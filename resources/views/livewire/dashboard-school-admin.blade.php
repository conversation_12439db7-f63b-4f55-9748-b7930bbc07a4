<div>
    <p>스쿨 어드민 대쉬보드 입니다.</p>
    <div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl">
        <div class="grid auto-rows-min gap-4 md:grid-cols-3">
            <br>
            <?
                /*
                if($sw_services == null){
                var_dump($sw_services);
                }else{
                    echo " 서비스 이용중";
                }
                */
            ?>

            @if($sw_services==null){
                <!--$sw_services == null-->
                <p>서비스가 없습니다.</p>
            }@else{
                <p>서비스 리스트</p>
                @foreach ($sw_services as $sw_service)
                    {{ $sw_service->service_name }} / {{  $sw_service->school_admin_id }}/ id : {{ auth()->user()->id}} <br>
                @endforeach
            
                @foreach ($sw_services as $sw_service)
                <div class="card bg-base-100 w-96 shadow-sm">
                        <!--<figure class="px-10 pt-10">
                            <img
                            
                            src="https://schoolworks.kr/img/bg-img/fram.png"
                            alt="Shoes"
                            class="rounded-xl" />
                        </figure>-->
                        <div class="card-body items-center text-center">
                            <h2 class="card-title">{{ $sw_service->service_name }}</h2>
                            <p>{{ $sw_service->service_detail }}</p>
                            <div class="card-actions">
                            <button class="btn btn-primary">서비스 상태 : 
                                


                                @if($sw_service->school_admin_id == auth()->user()->id) {{ "On" }}
                                @else {{ "Off" }}
                                @endif
                            </button>
                            </div>
                        </div>
                </div>
                @endforeach
            }@endif
        </div>
        <div class="relative h-full flex-1 overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-700">
            <x-placeholder-pattern class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20" />
        </div>
    </div>
</div>
