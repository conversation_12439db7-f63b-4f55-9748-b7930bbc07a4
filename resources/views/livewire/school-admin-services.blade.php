<div>
    <div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl p-4">
        <h2 class="text-xl font-bold">학교 서비스 관리</h2>
        
        @if(session()->has('message'))
            <div class="p-3 bg-green-100 text-green-700 rounded-md">
                {{ session('message') }}
            </div>
        @endif
        
        <div class="bg-white dark:bg-zinc-700 rounded-xl shadow p-4">
            <h3 class="text-lg font-semibold mb-4">사용 가능한 서비스</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">활성화하려는 서비스를 선택하세요. 활성화된 서비스는 같은 학교 코드를 가진 사용자에게 표시됩니다.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($services as $service)
                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-zinc-600 transition-colors">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium">{{ $service->service_name }}</h4>
                            <!-- Toggle Switch -->
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox"
                                    wire:click="toggleService({{ $service->id }})"
                                    class="sr-only peer"
                                    {{ in_array($service->id, $activeServices) ? 'checked' : '' }}>
                                <div class="relative w-16 h-8 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-600 peer-checked:bg-green-500 transition-colors duration-300 shadow-inner">
                                    <!-- 슬라이더 버튼 -->
                                    <div class="absolute top-1 left-1 bg-white border border-gray-300 rounded-full h-6 w-6 transition-transform duration-300 peer-checked:translate-x-8 shadow-md flex items-center justify-center">
                                        <!-- 활성화 상태 아이콘 (체크마크) -->
                                        <svg class="w-3 h-3 text-green-600 opacity-0 peer-checked:opacity-100 transition-opacity duration-200" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <!-- 비활성화 상태 아이콘 (X마크) -->
                                        <svg class="w-3 h-3 text-gray-400 opacity-100 peer-checked:opacity-0 transition-opacity duration-200" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- 배경 텍스트 -->
                                    <div class="absolute inset-0 flex items-center justify-between px-2 text-xs font-medium">
                                        <span class="text-white opacity-0 peer-checked:opacity-100 transition-opacity duration-200">ON</span>
                                        <span class="text-gray-600 opacity-100 peer-checked:opacity-0 transition-opacity duration-200">OFF</span>
                                    </div>
                                </div>
                            </label>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $service->service_detail }}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>