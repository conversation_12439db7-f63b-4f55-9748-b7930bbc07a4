<div>
    <div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl p-4">
        <h2 class="text-xl font-bold">학교 서비스 관리</h2>
        
        @if(session()->has('message'))
            <div class="p-3 bg-green-100 text-green-700 rounded-md">
                {{ session('message') }}
            </div>
        @endif
        
        <div class="bg-white dark:bg-zinc-700 rounded-xl shadow p-4">
            <h3 class="text-lg font-semibold mb-4">사용 가능한 서비스</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">활성화하려는 서비스를 선택하세요. 활성화된 서비스는 같은 학교 코드를 가진 사용자에게 표시됩니다.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($services as $service)
                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-zinc-600 transition-colors">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium">{{ $service->service_name }}</h4>
                            <!-- Toggle Switch -->
                            <div class="flex items-center justify-end">
                                <button
                                    wire:click="toggleService({{ $service->id }})"
                                    class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {{ in_array($service->id, $activeServices) ? 'bg-blue-600' : 'bg-gray-200' }}">
                                    <span class="sr-only">토글 서비스</span>
                                    <span class="inline-block h-4 w-4 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out {{ in_array($service->id, $activeServices) ? 'translate-x-6' : 'translate-x-1' }}"></span>
                                </button>
                                <span class="ml-2 text-xs font-medium {{ in_array($service->id, $activeServices) ? 'text-blue-600' : 'text-gray-500' }}">
                                    {{ in_array($service->id, $activeServices) ? 'ON' : 'OFF' }}
                                </span>
                            </div>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $service->service_detail }}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>