<div>
    <div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl p-4">
        <h2 class="text-xl font-bold">학교 서비스 관리</h2>
        
        @if(session()->has('message'))
            <div class="p-3 bg-green-100 text-green-700 rounded-md">
                {{ session('message') }}
            </div>
        @endif
        
        <div class="bg-white dark:bg-zinc-700 rounded-xl shadow p-4">
            <h3 class="text-lg font-semibold mb-4">사용 가능한 서비스</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">활성화하려는 서비스를 선택하세요. 활성화된 서비스는 같은 학교 코드를 가진 사용자에게 표시됩니다.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($services as $service)
                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-zinc-600 transition-colors">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium">{{ $service->service_name }}</h4>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" 
                                    wire:click="toggleService({{ $service->id }})" 
                                    class="sr-only peer"
                                    {{ in_array($service->id, $activeServices) ? 'checked' : '' }}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $service->service_detail }}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>