<div class="flex flex-col gap-6">
    <x-auth-header :title="__('Create an account')" :description="__('Enter your details below to create your account')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form wire:submit="register" class="flex flex-col gap-6">
       
        <!-- Admin ID -->
        <flux:input
            wire:model="admin_id"
            :label="__('admin_id')"
            type="text"
            required
            autocomplete=""
            placeholder=""
        />

        <!-- School Name Search -->
        <div class="mb-2">
            <flux:input
                wire:model="school_name"
                :label="__('School Name')"
                type="text"
                placeholder="Enter school name to search"
            />
            <div class="mt-2">
                <flux:button type="button" wire:click="searchSchool" class="w-full">
                    {{ __('Search School') }}
                </flux:button>
            </div>
        </div>

        <!-- School Search Results -->
        @if($schoolResults)
        <div class="p-4 bg-gray-100 dark:bg-zinc-700 rounded-lg mb-4">
            <h3 class="font-medium mb-2">{{ __('School Search Results') }}</h3>
            <div class="max-h-60 overflow-y-auto">
                @foreach($schoolResults as $school)
                {{ $school->SD_SCHUL_CODE  }}
                <div class="p-2 border-b border-gray-200 dark:border-zinc-600 hover:bg-gray-200 dark:hover:bg-zinc-600 cursor-pointer"
                wire:click="selectSchool('{{ $school->SD_SCHUL_CODE }}', '{{ $school->SCHUL_NM }}')">

                <p class="font-medium">{{ $school->SCHUL_NM }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-300">{{ $school->ORG_RDNMA }}</p>
                
                </div>


                <!-- 
                -->
                @endforeach
            </div>
        </div>
        @endif

        <!-- School Code and Name (Hidden) -->
        <input type="hidden" wire:model="school_nm">

        <!-- School Code -->
        <flux:input
            wire:model="school_code"
            :label="__('School Code')"
            type="text"
            required
            autocomplete=""
            placeholder="Enter your school code"
            readonly
        />

        <!-- Selected School Name (Read-only) -->
        @if($selected_school_name)
        <flux:input
            wire:model="selected_school_name"
            :label="__('Selected School')"
            type="text"
            readonly
        />
        @endif

        <!-- Password -->
        <flux:input
            wire:model="password"
            :label="__('Password')"
            type="password"
            required
            autocomplete="new-password"
            :placeholder="__('Password')"
        />

        <!-- Confirm Password -->
        <flux:input
            wire:model="password_confirmation"
            :label="__('Confirm password')"
            type="password"
            required
            autocomplete="new-password"
            :placeholder="__('Confirm password')"
        />

        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" class="w-full">
                {{ __('Create account') }}
            </flux:button>
        </div>
    </form>

    <div class="space-x-1 text-center text-sm text-zinc-600 dark:text-zinc-400">
        {{ __('Already have an account?') }}
        <flux:link :href="route('login')" wire:navigate>{{ __('Log in') }}</flux:link>
    </div>
</div>
