<div>
    <div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl">
        <h2 class="text-xl font-bold">Q&A 문의하기</h2>
        
        <form wire:submit.prevent="saveQuestion" class="max-w-lg mx-auto p-4 bg-white dark:bg-zinc-700 rounded-xl shadow">
            <!-- 히든 인풋박스 추가 -->
            <input type="hidden" id="request_user_no" wire:model="request_user_no">
            
            <div class="mb-4">
                <label for="question_title" class="block text-sm font-medium mb-2">질문 제목</label>
                <input type="text" id="question_title" wire:model="question_title" 
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" required>
                @error('question_title') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            
            <div class="mb-4">
                <label for="question_contents" class="block text-sm font-medium mb-2">질문 내용</label>
                <textarea id="question_contents" wire:model="question_contents" rows="6"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" required></textarea>
                @error('question_contents') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                    질문 등록하기
                </button>
            </div>
        </form>
        
        @if(session()->has('message'))
            <div class="max-w-lg mx-auto mt-4 p-3 bg-green-100 text-green-700 rounded-md">
                {{ session('message') }}
            </div>
        @endif
    </div>
</div>
