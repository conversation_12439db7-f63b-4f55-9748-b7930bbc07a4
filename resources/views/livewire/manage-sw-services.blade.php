<div>
    <div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl p-4">
        <h2 class="text-xl font-bold">서비스 관리</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 서비스 목록 -->
            <div class="bg-white dark:bg-zinc-700 rounded-xl shadow p-4">
                <h3 class="text-lg font-semibold mb-4">서비스 목록</h3>
                
                @if($services && count($services) > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                            <thead class="bg-gray-50 dark:bg-zinc-600">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">서비스명</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">상태</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">관리</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-zinc-700 divide-y divide-gray-200 dark:divide-gray-600">
                                @foreach($services as $service)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="font-medium">{{ $service->service_name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $service->service_state ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                                                {{ $service->service_state ? '활성화' : '비활성화' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button wire:click="editService({{ $service->id }})" class="text-blue-600 dark:text-blue-400 hover:underline mr-2">
                                                수정
                                            </button>
                                            <button wire:click="deleteService({{ $service->id }})" class="text-red-600 dark:text-red-400 hover:underline" onclick="return confirm('정말 삭제하시겠습니까?')">
                                                삭제
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-gray-500 dark:text-gray-400">등록된 서비스가 없습니다.</p>
                @endif
            </div>
            
            <!-- 서비스 등록/수정 폼 -->
            <div class="bg-white dark:bg-zinc-700 rounded-xl shadow p-4">
                <h3 class="text-lg font-semibold mb-4">
                    {{ $editing_id ? '서비스 수정' : '서비스 등록' }}
                </h3>
                
                <form wire:submit.prevent="saveService">
                    <div class="mb-4">
                        <label for="service_name" class="block text-sm font-medium mb-2">서비스명</label>
                        <input type="text" id="service_name" wire:model="service_name" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" required>
                        @error('service_name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                    
                    <div class="mb-4">
                        <label for="service_detail" class="block text-sm font-medium mb-2">서비스 상세정보</label>
                        <textarea id="service_detail" wire:model="service_detail" rows="6"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" required></textarea>
                        @error('service_detail') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        @if($editing_id)
                            <button type="button" wire:click="resetForm" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                                취소
                            </button>
                        @endif
                        <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                            {{ $editing_id ? '수정하기' : '등록하기' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        @if(session()->has('message'))
            <div class="mt-4 p-3 bg-green-100 text-green-700 rounded-md">
                {{ session('message') }}
            </div>
        @endif
    </div>
</div>