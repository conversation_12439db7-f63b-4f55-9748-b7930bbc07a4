<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\SwService;
use App\Models\ManageService;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('components.layouts.app')]
class SchoolAdminServices extends Component
{
    public $services;
    public $activeServices = [];
    
    public function mount()
    {
        $this->loadServices();
    }
    
    public function loadServices()
    {
        // 모든 서비스 가져오기
        $this->services = SwService::all();
        
        // 현재 학교 관리자가 활성화한 서비스 ID 목록 가져오기
        $activeServiceIds = ManageService::where('school_admin_id', Auth::id())
            ->where('is_active', true)
            ->pluck('sw_service_id')
            ->toArray();
        
        $this->activeServices = $activeServiceIds;
    }
    
    public function toggleService($serviceId)
    {
        $user = Auth::user();
        
        // 이미 해당 서비스에 대한 레코드가 있는지 확인
        $manageService = ManageService::where('school_admin_id', $user->id)
            ->where('sw_service_id', $serviceId)
            ->first();
        
        if ($manageService) {
            // 레코드가 있으면 활성화 상태 토글
            $manageService->update([
                'is_active' => !$manageService->is_active
            ]);
        } else {
            // 레코드가 없으면 새로 생성
            ManageService::create([
                'school_admin_id' => $user->id,
                'school_code' => $user->school_code,
                'sw_service_id' => $serviceId,
                'is_active' => true
            ]);
        }
        
        $this->loadServices();
        session()->flash('message', '서비스 상태가 변경되었습니다.');
    }
    
    public function render()
    {
        return view('livewire.school-admin-services');
    }
}