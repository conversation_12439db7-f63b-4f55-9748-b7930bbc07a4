<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\QnA;
use App\Models\QnaAnswer;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('components.layouts.app')]
class QnaAdmin extends Component
{
    public $questions;
    public $selectedQuestion = null;
    
    public $answer_title = '';
    public $answer_contents = '';
    public $replyToAnswerId = null;
    
    protected $rules = [
        'answer_title' => 'required|min:3|max:255',
        'answer_contents' => 'required|min:10',
    ];
    
    public function mount()
    {
        $this->loadQuestions();
    }
    
    public function loadQuestions()
    {
        // question_title이 null이 아닌 모든 질문 가져오기
        $this->questions = QnA::whereNotNull('question_title')->get();
    }
    
    public function selectQuestion($questionId)
    {
        $this->selectedQuestion = QnA::with(['topLevelAnswers.replies'])->find($questionId);
        $this->resetForm();
    }
    
    public function resetForm()
    {
        $this->answer_title = '';
        $this->answer_contents = '';
        $this->replyToAnswerId = null;
    }
    
    public function replyToAnswer($answerId)
    {
        $this->replyToAnswerId = $answerId;
        $answer = QnaAnswer::find($answerId);
        $this->answer_title = 'RE: ' . $answer->title;
        $this->answer_contents = '';
    }
    
    public function saveAnswer()
    {
        $this->validate();
        
        if ($this->selectedQuestion) {
            // 새 답변 생성
            QnaAnswer::create([
                'qna_id' => $this->selectedQuestion->id,
                'parent_id' => $this->replyToAnswerId,
                'admin_id' => Auth::id(),
                'title' => $this->answer_title,
                'contents' => $this->answer_contents,
            ]);
            
            // 질문 상태 업데이트
            $this->selectedQuestion->update([
                'question_state' => 1, // 답변 완료 상태로 변경
            ]);
            
            session()->flash('message', '답변이 성공적으로 등록되었습니다.');
            
            // 질문 다시 로드하여 답변 포함
            $this->selectedQuestion = QnA::with(['topLevelAnswers.replies'])->find($this->selectedQuestion->id);
            
            // 입력 필드 초기화
            $this->resetForm();
        }
    }
    
    public function render()
    {
        return view('livewire.qna-admin');
    }
}
