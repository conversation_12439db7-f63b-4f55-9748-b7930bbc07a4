<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\QnA as QnAModel;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('components.layouts.app')]
class Qna extends Component
{
    public $question_title = '';
    public $question_contents = '';
    public $request_user_no;
    
    public function mount()
    {
        // 컴포넌트가 로드될 때 로그인한 사용자의 ID를 설정
        $this->request_user_no = Auth::id();
    }
    
    protected $rules = [
        'question_title' => 'required|min:3|max:255',
        'question_contents' => 'required|min:10',
    ];
    
    public function saveQuestion()
    {
        $this->validate();
        
        QnAModel::create([
            'request_user_no' => $this->request_user_no,
            'question_title' => $this->question_title,
            'question_contents' => $this->question_contents,
            'question_state' => 0,
            'answer_admin_no' => 0,
            'answer_title' => '',
            'answer_contents' => '',
            'note01' => '',
            'note02' => ''
        ]);
        
        session()->flash('message', '질문이 성공적으로 등록되었습니다.');
        
        // 입력 필드 초기화
        $this->reset(['question_title', 'question_contents']);
    }
    
    public function render()
    {
        return view('livewire.qna');
    }
}
