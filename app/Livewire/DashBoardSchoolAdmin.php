<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Models\SwService;
use App\Models\ManageService;
use Illuminate\Support\Facades\DB;
use Throwable;

#[Layout('components.layouts.app')]
class DashboardSchoolAdmin extends Component
{
    public function getSwServiceData(){
        
    }


    public function render()
    {

        try{
            $sw_services = DB::table('sw_services')
            ->leftJoin('manage_services', 'sw_services.id', '=', 'manage_services.SwService_id')
            ->select('sw_services.*','manage_services.school_admin_id')
            //->where('manage_services.school_admin_id', 5)
            ->get();
        } catch (Throwable $e) {
            return view('livewire.dashboard-school-admin')->with([
                'sw_services' => null]);
        }

            return view('livewire.dashboard-school-admin')
            ->with([
            //'sw_services' => SwService::all(),
            'sw_services' =>DB::table('sw_services')
            ->leftJoin('manage_services', 'sw_services.id', '=', 'manage_services.SwService_id')
            ->select('sw_services.*','manage_services.school_admin_id')
            //->where('manage_services.school_admin_id', 5)
            ->get()]);
        
    }
}
