<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\SwService;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.app')]
class ManageSwServices extends Component
{
    public $services;
    public $service_name = '';
    public $service_detail = '';
    public $editing_id = null;
    
    protected $rules = [
        'service_name' => 'required|min:1|max:255',
    ];
    
    public function mount()
    {
        $this->loadServices();
    }
    
    public function loadServices()
    {
        $this->services = SwService::all();
    }
    
    public function saveService()
    {
        $this->validate();
        
        if ($this->editing_id) {
            // 기존 서비스 업데이트
            $service = SwService::find($this->editing_id);
            $service->update([
                'service_name' => $this->service_name,
                'service_detail' => $this->service_detail,
            ]);
            
            session()->flash('message', '서비스가 성공적으로 업데이트되었습니다.');
        } else {
            // 새 서비스 생성
            SwService::create([
                'service_name' => $this->service_name,
                'service_detail' => $this->service_detail,
                'service_state' => true,
            ]);
            
            session()->flash('message', '서비스가 성공적으로 등록되었습니다.');
        }
        
        $this->resetForm();
        $this->loadServices();
    }
    
    public function editService($id)
    {
        $service = SwService::find($id);
        $this->editing_id = $id;
        $this->service_name = $service->service_name;
        $this->service_detail = $service->service_detail;
    }
    
    public function deleteService($id)
    {
        SwService::destroy($id);
        session()->flash('message', '서비스가 삭제되었습니다.');
        $this->loadServices();
    }
    
    public function resetForm()
    {
        $this->editing_id = null;
        $this->service_name = '';
        $this->service_detail = '';
    }
    
    public function render()
    {
        return view('livewire.manage-sw-services');
    }
}