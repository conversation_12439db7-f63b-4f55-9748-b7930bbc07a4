<?php

namespace App\Livewire\Auth;

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('components.layouts.auth')]
class Register extends Component
{
    public string $admin_id = '';
    public string $password = '';
    public string $role = 'school_admin';
    public string $school_code = '';
    public string $school_nm = '';
    public string $school_name = '';
    public string $selected_school_name = '';
    public string $password_confirmation = '';
    public $schoolResults = null;

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $validated = $this->validate([
            'admin_id' => ['required', 'string', 'lowercase', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'string'],
            'school_code' => ['required', 'string'],
            'school_nm' => ['required', 'string'],
        ]);

        $validated['password'] = Hash::make($validated['password']);

        event(new Registered(($user = User::create($validated))));

        Auth::login($user);

        $this->redirect(route('dashboard', absolute: false), navigate: true);
    }

    /**
     * Search for schools by name
     */
    public function searchSchool(): void
    {
        if (empty($this->school_name)) {
            return;
        }

        $apiKey = '2f3df851084e46bb8938937b2e7672ee';
        $url = "https://open.neis.go.kr/hub/schoolInfo?SCHUL_NM=" . urlencode($this->school_name) . "&Type=json&pIndex=1&pSize=100&KEY=" . $apiKey;
        
        $response = file_get_contents($url);
        $data = json_decode($response);
        
        if (isset($data->schoolInfo)) {
            $this->schoolResults = $data->schoolInfo[1]->row;
        } else {
            $this->schoolResults = [];
            session()->flash('message', '검색 결과가 없습니다.');
        }
    }

    /**
     * Select a school from search results
     */
    public function selectSchool($schoolCode, $schoolName): void
    {
        $this->school_code = $schoolCode;
        $this->school_nm = $schoolName; // 학교명 저장
        $this->selected_school_name = $schoolName;
        $this->schoolResults = null; // Clear search results after selection
    }
}
