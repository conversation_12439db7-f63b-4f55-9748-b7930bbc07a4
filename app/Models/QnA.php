<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;

class QnA extends Model
{
    use HasFactory, Notifiable;

    // 테이블 이름 명시적으로 지정
    protected $table = 'qnas';

    protected $fillable = [
        'request_user_no',
        'question_title',
        'question_contents',
        'question_state',
        'answer_admin_no',
        'answer_title',
        'answer_contents',
        'note01',
        'note02'
    ];

    /**
     * 이 질문에 대한 모든 답변을 가져옵니다.
     */
    public function answers(): HasMany
    {
        return $this->hasMany(QnaAnswer::class, 'qna_id');
    }

    /**
     * 이 질문에 대한 최상위 답변만 가져옵니다 (부모가 없는 답변).
     */
    public function topLevelAnswers(): HasMany
    {
        return $this->hasMany(QnaAnswer::class, 'qna_id')->whereNull('parent_id');
    }
}
