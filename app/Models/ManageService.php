<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManageService extends Model
{
    use HasFactory;

    protected $fillable = [
        'school_admin_id',
        'school_code',
        'sw_service_id',
        'is_active',
    ];

    public function swService()
    {
        return $this->belongsTo(SwService::class, 'sw_service_id');
    }

    public function schoolAdmin()
    {
        return $this->belongsTo(User::class, 'school_admin_id');
    }
}
