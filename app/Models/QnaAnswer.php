<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class QnaAnswer extends Model
{
    use HasFactory;

    protected $fillable = [
        'qna_id',
        'parent_id',
        'admin_id',
        'title',
        'contents',
    ];

    /**
     * 이 답변이 속한 질문을 가져옵니다.
     */
    public function qna(): BelongsTo
    {
        return $this->belongsTo(QnA::class);
    }

    /**
     * 이 답변의 부모 답변을 가져옵니다.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(QnaAnswer::class, 'parent_id');
    }

    /**
     * 이 답변의 자식 답변들을 가져옵니다.
     */
    public function replies(): HasMany
    {
        return $this->hasMany(QnaAnswer::class, 'parent_id');
    }

    /**
     * 이 답변을 작성한 관리자를 가져옵니다.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }
}